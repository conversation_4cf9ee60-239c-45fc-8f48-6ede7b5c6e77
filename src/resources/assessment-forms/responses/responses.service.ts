import {
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';

import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { ChartData } from '../dto/chart-data.dto';
import { Assessment } from '../entities/assessment.entity';
import { Response } from '../entities/response.entity';
import type { UpdateResponseDto } from '../dto/updates/update-response.dto';
import type { CreateResponseDto } from '../dto/creates/create-response.dto';
import { Option } from '../entities/option.entity';
import { Question } from '../entities/question.entity';
import { Submission } from '../entities/submission.entity';
import { ItemBlockType } from '../enums/item-block-type.enum';

@Injectable()
export class ResponsesService {
  constructor(
    @InjectEntityManager()
    private entityManager: EntityManager,
    @InjectRepository(Assessment)
    private assessmentRepo: Repository<Assessment>,
    @InjectRepository(Response)
    private responseRepo: Repository<Response>,
  ) {}

  async create(createResponseDto: CreateResponseDto): Promise<Response> {
    const response = this.responseRepo.create(createResponseDto);
    return await this.responseRepo.save(response);
  }

  findAll() {
    return `This action returns all responses`;
  }

  findOne(id: number) {
    return `This action returns a #${id} response`;
  }

  async findAnswer(submission: number, question: number) {
    const responese = await this.responseRepo.findOne({
      where: { submissionId: submission, questionId: question },
    });

    return responese ?? {}; // ถ้าไม่เจอให้คืน {}
  }

  async update(
    id: number,
    updateResponseDto: UpdateResponseDto,
  ): Promise<Response> {
    await this.responseRepo.update(id, updateResponseDto);
    return await this.responseRepo.findOneOrFail({
      where: { id },
      relations: ['submission', 'selectedOption', 'question'],
    });
  }

  async remove(id: number): Promise<void> {
    await this.responseRepo.delete(id);
  }

  async userSaveQuizResponse(createResponseDto: CreateResponseDto) {
    let { submissionId, questionId, answerText, selectedOptionId } =
      createResponseDto;

    //validate user sent response is in time of submission and assessment
    const submission = await this.entityManager.findOne(Submission, {
      where: { id: createResponseDto.submissionId },
    });
    if (!submission) {
      throw new NotFoundException(
        `Submission with ID ${createResponseDto.submissionId} not found`,
      );
    }
    if (submission.endAt < new Date()) {
      throw new ForbiddenException('Submission is closed');
    }

    // validate user already submit assessment
    if (submission.submitAt) {
      throw new ForbiddenException('Submission already submitted');
    }

    // Only process answerText if there's no selectedOptionId provided
    if (answerText) {
      // find question
      const question = await this.entityManager.findOne(Question, {
        where: { id: questionId },
      });
      if (!question) {
        throw new NotFoundException(`Question with ID ${questionId} not found`);
      }

      // validate option is in question
      let existoOption = await this.entityManager.findOne(Option, {
        where: { itemBlockId: question.itemBlockId },
      });
      if (!existoOption) {
        existoOption = this.entityManager.create(Option, {
          itemBlockId: question.itemBlockId,
          sequence: 1,
          value: 0,
          nextSection: null,
        });
      }

      existoOption.optionText = answerText; // update or create option text

      await this.entityManager.save(Option, existoOption);
      selectedOptionId = existoOption.id;
    }

    // validate question is in response
    const existResponse = await this.entityManager.findOne(Response, {
      where: { submissionId: submissionId, questionId: questionId },
    });

    if (existResponse) {
      return await this.entityManager.save(Response, {
        ...existResponse,
        selectedOptionId,
      });
    } else {
      const response = this.entityManager.create(Response, createResponseDto);
      return this.entityManager.save(Response, response);
    }
  }
}
